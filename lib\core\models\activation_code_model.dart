/// نموذج كود التفعيل
class ActivationCodeModel {
  final String id;
  final String code;
  final String? prefix;
  final List<String> subjectIds;
  final bool isUsed;
  final String? usedByDeviceId;
  final DateTime? usedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final DateTime? expiresAt;

  const ActivationCodeModel({
    required this.id,
    required this.code,
    this.prefix,
    required this.subjectIds,
    this.isUsed = false,
    this.usedByDeviceId,
    this.usedAt,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.expiresAt,
  });

  /// التحقق من صلاحية الكود
  bool get isValid {
    if (!isActive || isUsed) return false;
    if (expiresAt != null && DateTime.now().isAfter(expiresAt!)) return false;
    return true;
  }

  /// التحقق من انتهاء صلاحية الكود
  bool get isExpired {
    return expiresAt != null && DateTime.now().isAfter(expiresAt!);
  }

  /// الحصول على الكود مع البادئة
  String get fullCode {
    return prefix != null ? '$prefix$code' : code;
  }

  /// إنشاء نموذج من JSON
  factory ActivationCodeModel.fromJson(Map<String, dynamic> json) {
    return ActivationCodeModel(
      id: json['id'] as String,
      code: json['code'] as String,
      prefix: json['prefix'] as String?,
      subjectIds: List<String>.from(json['subject_ids'] as List),
      isUsed: json['is_used'] as bool? ?? false,
      usedByDeviceId: json['used_by_device_id'] as String?,
      usedAt: json['used_at'] != null 
          ? DateTime.parse(json['used_at'] as String) 
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isActive: json['is_active'] as bool? ?? true,
      expiresAt: json['expires_at'] != null 
          ? DateTime.parse(json['expires_at'] as String) 
          : null,
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'prefix': prefix,
      'subject_ids': subjectIds,
      'is_used': isUsed,
      'used_by_device_id': usedByDeviceId,
      'used_at': usedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_active': isActive,
      'expires_at': expiresAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة من النموذج
  ActivationCodeModel copyWith({
    String? id,
    String? code,
    String? prefix,
    List<String>? subjectIds,
    bool? isUsed,
    String? usedByDeviceId,
    DateTime? usedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    DateTime? expiresAt,
  }) {
    return ActivationCodeModel(
      id: id ?? this.id,
      code: code ?? this.code,
      prefix: prefix ?? this.prefix,
      subjectIds: subjectIds ?? this.subjectIds,
      isUsed: isUsed ?? this.isUsed,
      usedByDeviceId: usedByDeviceId ?? this.usedByDeviceId,
      usedAt: usedAt ?? this.usedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ActivationCodeModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ActivationCodeModel(id: $id, code: $fullCode, isUsed: $isUsed, isValid: $isValid)';
  }
}
