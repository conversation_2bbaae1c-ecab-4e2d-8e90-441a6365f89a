import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// مزود الثيمات - يدير التبديل بين الوضع النهاري والليلي
class ThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'theme_mode';
  
  ThemeMode _themeMode = ThemeMode.system;
  
  ThemeMode get themeMode => _themeMode;
  
  bool get isDarkMode {
    if (_themeMode == ThemeMode.system) {
      // في حالة النظام، نحتاج للتحقق من إعدادات النظام
      return WidgetsBinding.instance.platformDispatcher.platformBrightness == Brightness.dark;
    }
    return _themeMode == ThemeMode.dark;
  }
  
  bool get isLightMode => !isDarkMode;
  
  /// تحميل إعدادات الثيم المحفوظة
  Future<void> loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeIndex = prefs.getInt(_themeKey) ?? 0;
      
      switch (themeIndex) {
        case 0:
          _themeMode = ThemeMode.system;
          break;
        case 1:
          _themeMode = ThemeMode.light;
          break;
        case 2:
          _themeMode = ThemeMode.dark;
          break;
        default:
          _themeMode = ThemeMode.system;
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات الثيم: $e');
    }
  }
  
  /// تغيير الثيم وحفظه
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;
    
    _themeMode = mode;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      int themeIndex;
      
      switch (mode) {
        case ThemeMode.system:
          themeIndex = 0;
          break;
        case ThemeMode.light:
          themeIndex = 1;
          break;
        case ThemeMode.dark:
          themeIndex = 2;
          break;
      }
      
      await prefs.setInt(_themeKey, themeIndex);
    } catch (e) {
      debugPrint('خطأ في حفظ إعدادات الثيم: $e');
    }
  }
  
  /// التبديل بين الوضع النهاري والليلي
  Future<void> toggleTheme() async {
    if (_themeMode == ThemeMode.light) {
      await setThemeMode(ThemeMode.dark);
    } else {
      await setThemeMode(ThemeMode.light);
    }
  }
  
  /// تعيين الثيم حسب النظام
  Future<void> setSystemTheme() async {
    await setThemeMode(ThemeMode.system);
  }
  
  /// تعيين الوضع النهاري
  Future<void> setLightTheme() async {
    await setThemeMode(ThemeMode.light);
  }
  
  /// تعيين الوضع الليلي
  Future<void> setDarkTheme() async {
    await setThemeMode(ThemeMode.dark);
  }
  
  /// الحصول على اسم الثيم الحالي
  String get currentThemeName {
    switch (_themeMode) {
      case ThemeMode.system:
        return 'النظام';
      case ThemeMode.light:
        return 'نهاري';
      case ThemeMode.dark:
        return 'ليلي';
    }
  }
  
  /// الحصول على أيقونة الثيم الحالي
  IconData get currentThemeIcon {
    switch (_themeMode) {
      case ThemeMode.system:
        return Icons.brightness_auto;
      case ThemeMode.light:
        return Icons.brightness_7;
      case ThemeMode.dark:
        return Icons.brightness_3;
    }
  }
}
