import 'package:flutter/material.dart';

/// مجموعة الألوان الأساسية للتطبيق التعليمي
/// تم تصميمها لتكون جذابة وعصرية ومناسبة للطلاب
class AppColors {
  AppColors._();

  // ========== الألوان الأساسية ==========

  /// اللون الأساسي - أزرق تعليمي جذاب
  static const Color primary = Color(0xFF2E7CE4);
  static const Color primaryDark = Color(0xFF1E5BB8);
  static const Color primaryLight = Color(0xFF5A9BF0);

  /// اللون الثانوي - بنفسجي أنيق
  static const Color secondary = Color(0xFF8B5FBF);
  static const Color secondaryDark = Color(0xFF6B4A8C);
  static const Color secondaryLight = Color(0xFFB085D9);

  /// اللون المميز - برتقالي دافئ
  static const Color accent = Color(0xFFFF6B35);
  static const Color accentDark = Color(0xFFE55A2B);
  static const Color accentLight = Color(0xFFFF8A5B);

  // ========== ألوان الحالة ==========

  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);

  // ========== الوضع النهاري ==========

  static const Color lightBackground = Color(0xFFFAFBFC);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color lightSurfaceVariant = Color(0xFFF8F9FA);
  static const Color lightOnBackground = Color(0xFF1A1A1A);
  static const Color lightOnSurface = Color(0xFF2D3748);
  static const Color lightOnSurfaceVariant = Color(0xFF64748B);

  // ========== الوضع الليلي ==========

  static const Color darkBackground = Color(0xFF0F172A);
  static const Color darkSurface = Color(0xFF1E293B);
  static const Color darkSurfaceVariant = Color(0xFF334155);
  static const Color darkOnBackground = Color(0xFFF8FAFC);
  static const Color darkOnSurface = Color(0xFFE2E8F0);
  static const Color darkOnSurfaceVariant = Color(0xFF94A3B8);

  // ========== ألوان الأقسام والمواد ==========

  /// مجموعة ألوان متنوعة للأقسام والمواد
  static const List<Color> sectionColors = [
    Color(0xFF6366F1), // Indigo
    Color(0xFF8B5CF6), // Violet
    Color(0xFFEC4899), // Pink
    Color(0xFFF59E0B), // Amber
    Color(0xFF10B981), // Emerald
    Color(0xFF06B6D4), // Cyan
    Color(0xFFEF4444), // Red
    Color(0xFF84CC16), // Lime
    Color(0xFFF97316), // Orange
    Color(0xFF3B82F6), // Blue
  ];

  // ========== ألوان التدرج ==========

  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, accentLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // ========== ألوان الشفافية ==========

  static Color primaryWithOpacity(double opacity) =>
      primary.withValues(alpha: opacity);
  static Color secondaryWithOpacity(double opacity) =>
      secondary.withValues(alpha: opacity);
  static Color accentWithOpacity(double opacity) =>
      accent.withValues(alpha: opacity);

  // ========== ألوان خاصة ==========

  /// لون الفيديوهات المقفلة
  static const Color lockedContent = Color(0xFF9CA3AF);

  /// لون الفيديوهات المتاحة
  static const Color unlockedContent = Color(0xFF10B981);

  /// لون الخلفية للكروت
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color cardBackgroundDark = Color(0xFF1E293B);

  /// لون الحدود
  static const Color border = Color(0xFFE5E7EB);
  static const Color borderDark = Color(0xFF374151);
}
