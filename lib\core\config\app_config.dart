/// تكوين التطبيق حسب النكهة
enum AppFlavor {
  student,
  admin,
}

class AppConfig {
  static AppFlavor? _flavor;
  
  static AppFlavor get flavor => _flavor ?? AppFlavor.student;
  
  static void setFlavor(AppFlavor flavor) {
    _flavor = flavor;
  }
  
  /// اسم التطبيق حسب النكهة
  static String get appName {
    switch (flavor) {
      case AppFlavor.student:
        return 'Smart Edu - الطالب';
      case AppFlavor.admin:
        return 'Smart Edu - الأدمن';
    }
  }
  
  /// معرف التطبيق
  static String get appId {
    switch (flavor) {
      case AppFlavor.student:
        return 'com.smartedu.student';
      case AppFlavor.admin:
        return 'com.smartedu.admin';
    }
  }
  
  /// أيقونة التطبيق
  static String get appIcon {
    switch (flavor) {
      case AppFlavor.student:
        return 'assets/icons/student_icon.png';
      case AppFlavor.admin:
        return 'assets/icons/admin_icon.png';
    }
  }
  
  /// لون التطبيق الأساسي
  static int get primaryColorValue {
    switch (flavor) {
      case AppFlavor.student:
        return 0xFF2E7CE4; // أزرق للطالب
      case AppFlavor.admin:
        return 0xFF8B5FBF; // بنفسجي للأدمن
    }
  }
  
  /// هل هو تطبيق الطالب؟
  static bool get isStudent => flavor == AppFlavor.student;
  
  /// هل هو تطبيق الأدمن؟
  static bool get isAdmin => flavor == AppFlavor.admin;
  
  /// الصفحة الرئيسية حسب النكهة
  static String get homeRoute {
    switch (flavor) {
      case AppFlavor.student:
        return '/student/home';
      case AppFlavor.admin:
        return '/admin/home';
    }
  }
}
