import 'package:flutter/material.dart';

/// نموذج الفيديو
class VideoModel {
  final String id;
  final String lessonId;
  final String name;
  final String? description;
  final Color color;
  final String? thumbnailUrl;
  final String? url360p;
  final String? url480p;
  final String? url720p;
  final String? url1080p;
  final int? duration; // بالثواني
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int order;

  const VideoModel({
    required this.id,
    required this.lessonId,
    required this.name,
    this.description,
    required this.color,
    this.thumbnailUrl,
    this.url360p,
    this.url480p,
    this.url720p,
    this.url1080p,
    this.duration,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.order = 0,
  });

  /// التحقق من وجود روابط فيديو
  bool get hasVideoUrls {
    return url360p != null || url480p != null || url720p != null || url1080p != null;
  }

  /// الحصول على أفضل جودة متاحة
  String? get bestQualityUrl {
    return url1080p ?? url720p ?? url480p ?? url360p;
  }

  /// الحصول على قائمة الجودات المتاحة
  List<VideoQuality> get availableQualities {
    final qualities = <VideoQuality>[];
    
    if (url360p != null) {
      qualities.add(VideoQuality(quality: '360p', url: url360p!));
    }
    if (url480p != null) {
      qualities.add(VideoQuality(quality: '480p', url: url480p!));
    }
    if (url720p != null) {
      qualities.add(VideoQuality(quality: '720p', url: url720p!));
    }
    if (url1080p != null) {
      qualities.add(VideoQuality(quality: '1080p', url: url1080p!));
    }
    
    return qualities;
  }

  /// تنسيق مدة الفيديو
  String get formattedDuration {
    if (duration == null) return '';
    
    final hours = duration! ~/ 3600;
    final minutes = (duration! % 3600) ~/ 60;
    final seconds = duration! % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// إنشاء نموذج من JSON
  factory VideoModel.fromJson(Map<String, dynamic> json) {
    return VideoModel(
      id: json['id'] as String,
      lessonId: json['lesson_id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      color: Color(json['color'] as int),
      thumbnailUrl: json['thumbnail_url'] as String?,
      url360p: json['url_360p'] as String?,
      url480p: json['url_480p'] as String?,
      url720p: json['url_720p'] as String?,
      url1080p: json['url_1080p'] as String?,
      duration: json['duration'] as int?,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      order: json['order'] as int? ?? 0,
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'lesson_id': lessonId,
      'name': name,
      'description': description,
      'color': color.value,
      'thumbnail_url': thumbnailUrl,
      'url_360p': url360p,
      'url_480p': url480p,
      'url_720p': url720p,
      'url_1080p': url1080p,
      'duration': duration,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'order': order,
    };
  }

  /// إنشاء نسخة معدلة من النموذج
  VideoModel copyWith({
    String? id,
    String? lessonId,
    String? name,
    String? description,
    Color? color,
    String? thumbnailUrl,
    String? url360p,
    String? url480p,
    String? url720p,
    String? url1080p,
    int? duration,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? order,
  }) {
    return VideoModel(
      id: id ?? this.id,
      lessonId: lessonId ?? this.lessonId,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      url360p: url360p ?? this.url360p,
      url480p: url480p ?? this.url480p,
      url720p: url720p ?? this.url720p,
      url1080p: url1080p ?? this.url1080p,
      duration: duration ?? this.duration,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      order: order ?? this.order,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VideoModel(id: $id, name: $name, lessonId: $lessonId, isActive: $isActive)';
  }
}

/// نموذج جودة الفيديو
class VideoQuality {
  final String quality;
  final String url;

  const VideoQuality({
    required this.quality,
    required this.url,
  });

  @override
  String toString() => quality;
}
