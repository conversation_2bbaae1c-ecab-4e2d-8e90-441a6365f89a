/// نموذج الاشتراك
class SubscriptionModel {
  final String id;
  final String deviceId;
  final String subjectId;
  final String activationCodeId;
  final DateTime activatedAt;
  final DateTime? expiresAt;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SubscriptionModel({
    required this.id,
    required this.deviceId,
    required this.subjectId,
    required this.activationCodeId,
    required this.activatedAt,
    this.expiresAt,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  /// التحقق من صلاحية الاشتراك
  bool get isValid {
    if (!isActive) return false;
    if (expiresAt != null && DateTime.now().isAfter(expiresAt!)) return false;
    return true;
  }

  /// التحقق من انتهاء صلاحية الاشتراك
  bool get isExpired {
    return expiresAt != null && DateTime.now().isAfter(expiresAt!);
  }

  /// عدد الأيام المتبقية
  int? get daysRemaining {
    if (expiresAt == null) return null;
    final difference = expiresAt!.difference(DateTime.now());
    return difference.inDays;
  }

  /// إنشاء نموذج من JSON
  factory SubscriptionModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionModel(
      id: json['id'] as String,
      deviceId: json['device_id'] as String,
      subjectId: json['subject_id'] as String,
      activationCodeId: json['activation_code_id'] as String,
      activatedAt: DateTime.parse(json['activated_at'] as String),
      expiresAt: json['expires_at'] != null 
          ? DateTime.parse(json['expires_at'] as String) 
          : null,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'device_id': deviceId,
      'subject_id': subjectId,
      'activation_code_id': activationCodeId,
      'activated_at': activatedAt.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة من النموذج
  SubscriptionModel copyWith({
    String? id,
    String? deviceId,
    String? subjectId,
    String? activationCodeId,
    DateTime? activatedAt,
    DateTime? expiresAt,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SubscriptionModel(
      id: id ?? this.id,
      deviceId: deviceId ?? this.deviceId,
      subjectId: subjectId ?? this.subjectId,
      activationCodeId: activationCodeId ?? this.activationCodeId,
      activatedAt: activatedAt ?? this.activatedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SubscriptionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SubscriptionModel(id: $id, subjectId: $subjectId, isValid: $isValid)';
  }
}
