import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  static const String url = 'https://gkfihpsqumypzdosjfjq.supabase.co';
  static const String anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdrZmlocHNxdW15cHpkb3NqZmpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ2MDQyNzMsImV4cCI6MjA3MDE4MDI3M30.QUFVqY__uYtzc70M-_5YIv-QvS18TT6SdIGNmN1tQbc';
  
  static SupabaseClient get client => Supabase.instance.client;
  
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: url,
      anonKey: anon<PERSON><PERSON>,
    );
  }
}
