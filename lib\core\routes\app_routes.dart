/// مسارات التطبيق
class AppRoutes {
  AppRoutes._();

  // ========== المسارات الأساسية ==========
  
  static const String splash = '/';
  static const String home = '/home';
  static const String login = '/login';
  
  // ========== مسارات الطالب ==========
  
  static const String studentHome = '/student/home';
  static const String videos = '/student/videos';
  static const String tests = '/student/tests';
  static const String subscription = '/student/subscription';
  
  // مسارات الفيديوهات
  static const String videoSections = '/student/videos/sections';
  static const String videoSubjects = '/student/videos/subjects';
  static const String videoUnits = '/student/videos/units';
  static const String videoLessons = '/student/videos/lessons';
  static const String videoPlayer = '/student/videos/player';
  
  // مسارات الاختبارات
  static const String testSections = '/student/tests/sections';
  static const String testSubjects = '/student/tests/subjects';
  static const String testUnits = '/student/tests/units';
  static const String testQuestions = '/student/tests/questions';
  static const String testResults = '/student/tests/results';
  
  // ========== مسارات الأدمن ==========
  
  static const String adminHome = '/admin/home';
  static const String adminLogin = '/admin/login';
  
  // إدارة الفيديوهات
  static const String adminVideos = '/admin/videos';
  static const String adminVideoSections = '/admin/videos/sections';
  static const String adminVideoSubjects = '/admin/videos/subjects';
  static const String adminVideoUnits = '/admin/videos/units';
  static const String adminVideoLessons = '/admin/videos/lessons';
  static const String adminVideoManagement = '/admin/videos/management';
  
  // إدارة الاختبارات
  static const String adminTests = '/admin/tests';
  static const String adminTestSections = '/admin/tests/sections';
  static const String adminTestSubjects = '/admin/tests/subjects';
  static const String adminTestUnits = '/admin/tests/units';
  static const String adminTestQuestions = '/admin/tests/questions';
  
  // إدارة الأكواد
  static const String adminCodes = '/admin/codes';
  static const String adminCodeGeneration = '/admin/codes/generate';
  static const String adminCodeManagement = '/admin/codes/management';
  
  // ========== مسارات الإعدادات ==========
  
  static const String settings = '/settings';
  static const String profile = '/profile';
  static const String about = '/about';
  static const String help = '/help';
  
  // ========== قائمة جميع المسارات ==========
  
  static const List<String> allRoutes = [
    splash,
    home,
    login,
    studentHome,
    videos,
    tests,
    subscription,
    videoSections,
    videoSubjects,
    videoUnits,
    videoLessons,
    videoPlayer,
    testSections,
    testSubjects,
    testUnits,
    testQuestions,
    testResults,
    adminHome,
    adminLogin,
    adminVideos,
    adminVideoSections,
    adminVideoSubjects,
    adminVideoUnits,
    adminVideoLessons,
    adminVideoManagement,
    adminTests,
    adminTestSections,
    adminTestSubjects,
    adminTestUnits,
    adminTestQuestions,
    adminCodes,
    adminCodeGeneration,
    adminCodeManagement,
    settings,
    profile,
    about,
    help,
  ];
}
