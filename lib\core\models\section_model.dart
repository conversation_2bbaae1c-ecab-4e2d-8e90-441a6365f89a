import 'package:flutter/material.dart';

/// نموذج القسم (مثل البكالوريا العلمي، البكالوريا الأدبي، الصف التاسع)
class SectionModel {
  final String id;
  final String name;
  final String? description;
  final Color color;
  final bool isPaid;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? imageUrl;
  final int order;

  const SectionModel({
    required this.id,
    required this.name,
    this.description,
    required this.color,
    required this.isPaid,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.imageUrl,
    this.order = 0,
  });

  /// إنشاء نموذج من JSON
  factory SectionModel.fromJson(Map<String, dynamic> json) {
    return SectionModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      color: Color(json['color'] as int),
      isPaid: json['is_paid'] as bool,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      imageUrl: json['image_url'] as String?,
      order: json['order'] as int? ?? 0,
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color.value,
      'is_paid': isPaid,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'image_url': imageUrl,
      'order': order,
    };
  }

  /// إنشاء نسخة معدلة من النموذج
  SectionModel copyWith({
    String? id,
    String? name,
    String? description,
    Color? color,
    bool? isPaid,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? imageUrl,
    int? order,
  }) {
    return SectionModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      isPaid: isPaid ?? this.isPaid,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      imageUrl: imageUrl ?? this.imageUrl,
      order: order ?? this.order,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SectionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SectionModel(id: $id, name: $name, isPaid: $isPaid, isActive: $isActive)';
  }
}
