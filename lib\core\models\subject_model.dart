import 'package:flutter/material.dart';

/// نموذج المادة (مثل الرياضيات، الفيزياء، الكيمياء)
class SubjectModel {
  final String id;
  final String sectionId;
  final String name;
  final String? description;
  final Color color;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? imageUrl;
  final int order;

  const SubjectModel({
    required this.id,
    required this.sectionId,
    required this.name,
    this.description,
    required this.color,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.imageUrl,
    this.order = 0,
  });

  /// إنشاء نموذج من JSON
  factory SubjectModel.fromJson(Map<String, dynamic> json) {
    return SubjectModel(
      id: json['id'] as String,
      sectionId: json['section_id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      color: Color(json['color'] as int),
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      imageUrl: json['image_url'] as String?,
      order: json['order'] as int? ?? 0,
    );
  }

  /// تحويل النموذج إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'section_id': sectionId,
      'name': name,
      'description': description,
      'color': color.value,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'image_url': imageUrl,
      'order': order,
    };
  }

  /// إنشاء نسخة معدلة من النموذج
  SubjectModel copyWith({
    String? id,
    String? sectionId,
    String? name,
    String? description,
    Color? color,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? imageUrl,
    int? order,
  }) {
    return SubjectModel(
      id: id ?? this.id,
      sectionId: sectionId ?? this.sectionId,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      imageUrl: imageUrl ?? this.imageUrl,
      order: order ?? this.order,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SubjectModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SubjectModel(id: $id, name: $name, sectionId: $sectionId, isActive: $isActive)';
  }
}
